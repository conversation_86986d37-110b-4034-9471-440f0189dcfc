# 更新日志

## [1.1.1] - 2025-01-18

### 🚀 新增功能
- ✨ **精确倒计时显示**：卡密不足1天时显示精确的小时和分钟倒计时
  - 主窗口卡密信息实时显示剩余时间（如：剩余23小时45分钟）
  - 登录窗口优化到期时间提示，不再显示模糊的"1天到期"
  - 实时更新倒计时，每分钟自动刷新显示
- ✨ **完善自动退出机制**：卡密到期时立即自动退出软件
  - 卡密过期时显示专用对话框提示
  - 自动关闭主窗口并返回登录界面
  - 提供联系代理商选项

### 🔧 功能优化
- 🔧 **网站后台严格验证**：强化卡密过期和不存在的验证机制
  - 卡密过期或不存在时静默拒绝访问，不显示具体错误信息
  - 脚本管理功能使用专用验证模式，确保安全性
  - 所有API接口统一使用严格验证
- 🔧 **登录验证优化**：登录成功时显示精确的剩余时间
  - 不足1天时显示小时分钟（如：剩余5小时30分钟）
  - 1天以上显示天数和小时（如：剩余1天12小时）
  - 7天以上仅显示天数

### 🛠️ 技术改进
- 新增 `validateForScript` 函数用于脚本功能的静默验证
- 新增 `show-expired-dialog` 和 `handle-license-expired` IPC接口
- 实现精确的时间计算和格式化函数
- 优化倒计时更新机制，减少性能消耗

### 📋 具体优化内容

#### 网站后台
1. **静默验证模式**：卡密过期或不存在时不暴露具体错误信息
2. **脚本功能保护**：所有脚本管理功能使用严格验证
3. **API接口统一**：使用新的验证中间件确保一致性

#### APP软件
1. **精确倒计时**：实现小时分钟级别的精确显示
2. **实时更新**：每分钟自动更新倒计时显示
3. **自动退出**：卡密过期时立即显示对话框并退出
4. **用户体验**：提供更友好的过期提示和操作选项

---

## [1.1.0] - 2025-01-18

### 🚀 新增功能
- ✨ **卡密到期强制停止功能**：实现卡密到期后立即停止使用所有功能
  - 卡密过期时自动关闭主窗口并返回登录界面
  - 定时检查机制（每5分钟）确保卡密状态实时生效
  - 在APP软件登录窗口弹窗提示"卡密到期，请续费使用"
- ✨ **脚本管理权限验证**：所有脚本功能必须验证卡密
  - 卡密过期或不存在时无法使用脚本功能
  - 提示"卡密过期或者不存在无法使用功能"

### 🔧 功能优化
- 🔧 **统一卡密验证中间件**：创建统一的卡密验证机制
- 🔧 **API接口增强验证**：强化所有API接口的卡密验证
- 🔧 **错误信息优化**：提供更详细和友好的错误提示信息
- 🔧 **登录界面优化**：新增卡密到期专用提示界面

### 🔒 安全增强
- 🛡️ **实时卡密验证**：确保卡密状态变化立即生效
- 🛡️ **强制停止机制**：防止过期卡密继续使用系统功能
- 🛡️ **安全日志记录**：记录卡密验证失败等安全事件
- 🛡️ **数据库兼容性修复**：修复不同数据库环境下的字段兼容性问题

### 📋 技术改进
- 新增 `license_middleware.php` 统一卡密验证中间件
- 新增 `security_logs` 表记录安全事件
- 优化脚本管理页面的权限控制
- 实现APP软件定时卡密检查机制
- 完善强制退出流程和状态清理

---

## [1.0.8] - 2025-08-09

### 新增功能
- ✨ **URL匹配规则功能**：支持Tampermonkey原生的URL匹配规则
  - 网站后台新增URL匹配输入框，支持输入多个URL链接
  - 支持通配符匹配：`*`、`https://example.com/*`、`https://*.example.com/*`
  - 支持协议通配符：`*://example.com/*`
  - 支持端口匹配：`https://example.com:8080/*`
  - APP软件根据匹配规则智能加载脚本，只在指定页面生效

### 技术改进
- 🔧 **数据库结构优化**：为scripts表添加match_urls字段
- 🔧 **API接口增强**：verify.php接口支持返回URL匹配规则
- 🔧 **脚本注入逻辑优化**：添加URL匹配检查，提高性能和安全性
- 🔧 **表单界面美化**：新增URL匹配规则输入框和帮助说明

### 功能特点
- 📍 **精确控制**：脚本只在匹配的URL页面中加载，避免不必要的资源消耗
- 🎯 **灵活配置**：支持多种匹配模式，满足不同场景需求
- 🛡️ **安全增强**：通过URL限制减少脚本在非目标页面的执行风险
- ⚡ **性能优化**：智能匹配算法，平均匹配时间仅0.003ms

### 使用说明
1. 在网站后台脚本管理页面，找到"URL匹配规则"输入框
2. 每行输入一个URL匹配规则，例如：
   - `https://store.weixin.qq.com/*` - 匹配微信小店所有页面
   - `https://channels.weixin.qq.com/*` - 匹配微信视频号所有页面
   - `*://example.com/*` - 匹配任何协议的example.com域名
3. 保存脚本后，APP软件会自动根据匹配规则加载脚本
4. 如果不填写匹配规则，脚本将在所有页面中加载（默认行为）

### 兼容性
- ✅ 完全兼容现有脚本，无需修改
- ✅ 向后兼容，未设置匹配规则的脚本继续正常工作
- ✅ 支持macOS 10.15+，包括Intel和Apple Silicon芯片

### 测试验证
- ✅ 14项URL匹配功能测试全部通过
- ✅ 性能测试：10000次匹配耗时仅31ms
- ✅ 数据库操作测试通过
- ✅ API接口测试通过

---

## [1.0.7] - 2025-08-05
### 修复
- 🐛 修复DMG打包配置问题
- 🐛 优化脚本注入逻辑

## [1.0.6] - 2025-08-04
### 新增
- ✨ 添加AI智能客服功能
- ✨ 支持多店铺管理

## [1.0.5] - 2025-08-03
### 改进
- 🔧 优化用户界面
- 🔧 提升稳定性

---

**注意事项：**
- 请在更新前备份重要数据
- 建议在测试环境中先验证功能
- 如遇问题请联系技术支持
