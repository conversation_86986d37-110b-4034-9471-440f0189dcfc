#!/usr/bin/env node

/**
 * 小梅花AI智能客服 v1.1.0 构建脚本
 * 卡密到期强制停止功能版本
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建小梅花AI智能客服 v1.1.0');
console.log('📋 本版本新增功能：');
console.log('   - 卡密到期强制停止功能');
console.log('   - 定时卡密检查机制');
console.log('   - 脚本管理权限验证');
console.log('   - 优化登录界面提示');
console.log('');

// 检查当前目录
const currentDir = process.cwd();
const packageJsonPath = path.join(currentDir, 'package.json');

if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ 错误：请在项目根目录运行此脚本');
  process.exit(1);
}

// 读取package.json验证版本
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
if (packageJson.version !== '1.1.0') {
  console.error(`❌ 错误：package.json版本不匹配，当前版本：${packageJson.version}，期望版本：1.1.0`);
  process.exit(1);
}

console.log('✅ 版本验证通过：v1.1.0');

try {
  // 步骤1：清理之前的构建
  console.log('\n🧹 步骤1：清理之前的构建文件...');
  try {
    execSync('npm run clean', { stdio: 'inherit' });
    console.log('✅ 清理完成');
  } catch (error) {
    console.log('⚠️ 清理失败，继续构建...');
  }

  // 步骤2：验证配置
  console.log('\n🔍 步骤2：验证构建配置...');
  try {
    execSync('node scripts/validate-config.js', { stdio: 'inherit' });
    console.log('✅ 配置验证通过');
  } catch (error) {
    console.log('⚠️ 配置验证失败，继续构建...');
  }

  // 步骤3：构建macOS版本
  console.log('\n🔨 步骤3：构建macOS版本...');
  console.log('   构建目标：ARM64 + x64 通用版本');
  
  try {
    // 构建ARM64版本
    console.log('   📱 构建ARM64版本...');
    execSync('electron-builder --mac --arm64', { stdio: 'inherit' });
    
    // 构建x64版本
    console.log('   💻 构建x64版本...');
    execSync('electron-builder --mac --x64', { stdio: 'inherit' });
    
    console.log('✅ macOS版本构建完成');
  } catch (error) {
    console.error('❌ macOS版本构建失败:', error.message);
    process.exit(1);
  }

  // 步骤4：验证构建结果
  console.log('\n🔍 步骤4：验证构建结果...');
  const distDir = path.join(currentDir, 'dist');
  
  if (!fs.existsSync(distDir)) {
    console.error('❌ 构建失败：dist目录不存在');
    process.exit(1);
  }

  const files = fs.readdirSync(distDir);
  const dmgFiles = files.filter(file => file.endsWith('.dmg'));
  
  if (dmgFiles.length === 0) {
    console.error('❌ 构建失败：未找到DMG文件');
    process.exit(1);
  }

  console.log('✅ 构建验证通过');
  console.log('📦 生成的文件：');
  dmgFiles.forEach(file => {
    const filePath = path.join(distDir, file);
    const stats = fs.statSync(filePath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`   - ${file} (${sizeInMB} MB)`);
  });

  // 步骤5：创建发布信息
  console.log('\n📝 步骤5：创建发布信息...');
  const releaseInfo = {
    version: '1.1.0',
    buildDate: new Date().toISOString(),
    features: [
      '卡密到期强制停止功能',
      '定时卡密检查机制（每5分钟）',
      '脚本管理权限验证',
      '优化登录界面提示',
      '统一卡密验证中间件',
      'API接口增强验证',
      '安全日志记录'
    ],
    files: dmgFiles.map(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        sizeFormatted: `${(stats.size / (1024 * 1024)).toFixed(2)} MB`
      };
    })
  };

  const releaseInfoPath = path.join(distDir, 'release-info-v1.1.0.json');
  fs.writeFileSync(releaseInfoPath, JSON.stringify(releaseInfo, null, 2));
  console.log(`✅ 发布信息已保存到：${releaseInfoPath}`);

  // 步骤6：创建安装说明
  console.log('\n📋 步骤6：创建安装说明...');
  const installInstructions = `# 小梅花AI智能客服 v1.1.0 安装说明

## 🚀 新版本特性
- **卡密到期强制停止功能**：卡密过期后立即停止使用所有功能
- **定时卡密检查机制**：每5分钟自动检查卡密状态
- **脚本管理权限验证**：所有脚本功能需要有效卡密
- **优化登录界面提示**：更友好的错误提示和到期提醒

## 📦 安装步骤
1. 下载对应您设备架构的DMG文件：
   - ARM64版本：适用于M1/M2/M3芯片的Mac
   - x64版本：适用于Intel芯片的Mac

2. 双击DMG文件打开安装包

3. 将"小梅花AI智能客服"拖拽到"Applications"文件夹

4. 在应用程序中找到并启动"小梅花AI智能客服"

## ⚠️ 重要提醒
- 本版本加强了卡密验证机制，请确保使用有效的卡密
- 卡密到期后需要联系代理商续费才能继续使用
- 建议定期检查卡密到期时间，避免影响正常使用

## 🔄 从旧版本升级
1. 备份重要数据（如有）
2. 卸载旧版本应用
3. 安装新版本
4. 重新验证卡密

## 📞 技术支持
如遇到问题，请联系技术支持或代理商。

---
构建时间：${new Date().toLocaleString('zh-CN')}
版本：v1.1.0
`;

  const installInstructionsPath = path.join(distDir, '安装说明-v1.1.0.md');
  fs.writeFileSync(installInstructionsPath, installInstructions);
  console.log(`✅ 安装说明已保存到：${installInstructionsPath}`);

  // 完成
  console.log('\n🎉 构建完成！');
  console.log('📁 构建文件位置：', distDir);
  console.log('\n📋 构建摘要：');
  console.log(`   版本：v${packageJson.version}`);
  console.log(`   构建时间：${new Date().toLocaleString('zh-CN')}`);
  console.log(`   文件数量：${dmgFiles.length} 个DMG文件`);
  console.log('\n🚀 可以开始分发新版本了！');

} catch (error) {
  console.error('\n❌ 构建失败:', error.message);
  process.exit(1);
}
