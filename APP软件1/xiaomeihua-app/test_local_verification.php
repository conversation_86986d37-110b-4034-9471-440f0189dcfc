<?php
/**
 * 本地卡密验证测试脚本
 * 直接测试修复后的验证逻辑
 */

// 模拟数据库连接
class MockPDO {
    public function prepare($sql) {
        return new MockPDOStatement($sql);
    }
    
    public function query($sql) {
        return true;
    }
}

class MockPDOStatement {
    private $sql;
    
    public function __construct($sql) {
        $this->sql = $sql;
    }
    
    public function execute($params = []) {
        return true;
    }
    
    public function fetch($mode = null) {
        // 模拟不存在的卡密
        if (strpos($this->sql, 'SELECT') !== false) {
            return false; // 模拟查询不到结果
        }
        return true;
    }
}

// 包含修复后的中间件
require_once '../../网站后台 /includes/license_middleware.php';

// 模拟全局变量
$pdo = new MockPDO();

// 测试用例
$testCases = [
    [
        'name' => '空卡密测试',
        'key' => '',
        'expected_error' => 'INVALID_FORMAT'
    ],
    [
        'name' => '不存在的卡密测试',
        'key' => 'NONEXISTENT-KEY-12345',
        'expected_error' => 'KEY_NOT_FOUND'
    ],
    [
        'name' => '超长卡密测试',
        'key' => str_repeat('A', 300),
        'expected_error' => 'INVALID_FORMAT'
    ]
];

echo "🧪 本地卡密验证测试\n";
echo "==================\n\n";

foreach ($testCases as $testCase) {
    echo "测试: {$testCase['name']}\n";
    echo "卡密: " . (empty($testCase['key']) ? '(空)' : substr($testCase['key'], 0, 20) . '...') . "\n";
    
    try {
        $middleware = new LicenseMiddleware($pdo, true);
        $result = $middleware->quickValidate($testCase['key']);
        
        echo "结果: " . ($result['success'] ? '成功' : '失败') . "\n";
        if (!$result['success']) {
            echo "错误代码: " . ($result['error_code'] ?? '无') . "\n";
            echo "错误消息: " . $result['message'] . "\n";
            
            if (($result['error_code'] ?? '') === $testCase['expected_error']) {
                echo "✅ 测试通过\n";
            } else {
                echo "❌ 测试失败 - 期望: {$testCase['expected_error']}, 实际: " . ($result['error_code'] ?? '无') . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "🎯 本地测试完成\n";
?>
