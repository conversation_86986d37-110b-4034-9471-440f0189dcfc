#!/usr/bin/env node

/**
 * 卡密验证修复测试脚本
 * 测试各种状态的卡密验证功能
 */

const axios = require('axios');

// 生成随机卡密用于测试
function generateRandomKey() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 20; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 测试配置
const TEST_CONFIG = {
  apiUrl: 'https://xiaomeihuakefu.cn/api/verify.php',
  testCases: [
    {
      name: '不存在的卡密',
      key: generateRandomKey(), // 使用随机生成的卡密确保不存在
      expectedError: 'KEY_NOT_FOUND',
      description: '测试不存在的卡密是否正确返回KEY_NOT_FOUND错误'
    },
    {
      name: '格式错误的卡密（太短）',
      key: 'abc',
      expectedError: 'KEY_NOT_FOUND', // 短卡密会被当作不存在处理
      description: '测试格式错误的卡密是否正确处理'
    },
    {
      name: '空卡密',
      key: '',
      expectedError: 'MISSING_KEY',
      description: '测试空卡密是否正确处理'
    },
    {
      name: '超长卡密',
      key: 'A'.repeat(300), // 超过255字符限制
      expectedError: 'INVALID_FORMAT',
      description: '测试超长卡密是否正确返回INVALID_FORMAT错误'
    }
  ]
};

/**
 * 发送验证请求
 */
async function testVerification(testCase) {
  try {
    console.log(`\n🧪 测试: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log(`🔑 卡密: ${testCase.key || '(空)'}`);
    
    const response = await axios.post(TEST_CONFIG.apiUrl, {
      key: testCase.key,
      check_status: 1
    }, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 10000,
      validateStatus: function (status) {
        return status >= 200 && status < 600; // 接受所有状态码
      }
    });

    console.log(`📊 HTTP状态码: ${response.status}`);
    
    if (response.data) {
      console.log(`✅ 响应数据:`, JSON.stringify(response.data, null, 2));
      
      // 检查是否符合预期
      if (!response.data.success) {
        if (response.data.error_code === testCase.expectedError) {
          console.log(`✅ 测试通过: 错误代码符合预期 (${testCase.expectedError})`);
          return { success: true, testCase, response: response.data };
        } else {
          console.log(`❌ 测试失败: 期望错误代码 ${testCase.expectedError}, 实际 ${response.data.error_code}`);
          return { success: false, testCase, response: response.data, reason: 'error_code_mismatch' };
        }
      } else {
        console.log(`❌ 测试失败: 期望验证失败，但实际成功了`);
        return { success: false, testCase, response: response.data, reason: 'unexpected_success' };
      }
    } else {
      console.log(`❌ 测试失败: 无响应数据`);
      return { success: false, testCase, reason: 'no_response_data' };
    }
    
  } catch (error) {
    console.log(`❌ 请求错误:`, error.message);
    
    // 对于网络错误，检查是否是预期的行为
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      console.log(`⚠️  网络连接问题，跳过此测试`);
      return { success: true, testCase, skipped: true, reason: 'network_error' };
    }
    
    return { success: false, testCase, error: error.message };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始卡密验证修复测试...\n');
  console.log(`📡 API地址: ${TEST_CONFIG.apiUrl}`);
  
  const results = [];
  
  for (const testCase of TEST_CONFIG.testCases) {
    const result = await testVerification(testCase);
    results.push(result);
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 输出测试总结
  console.log('\n📋 测试总结:');
  console.log('=' * 50);
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success && !r.skipped).length;
  const skipped = results.filter(r => r.skipped).length;
  
  console.log(`✅ 通过: ${passed}`);
  console.log(`❌ 失败: ${failed}`);
  console.log(`⚠️  跳过: ${skipped}`);
  console.log(`📊 总计: ${results.length}`);
  
  if (failed > 0) {
    console.log('\n❌ 失败的测试:');
    results.filter(r => !r.success && !r.skipped).forEach(r => {
      console.log(`  - ${r.testCase.name}: ${r.reason || r.error}`);
    });
  }
  
  console.log('\n🎯 修复验证完成!');
  
  return {
    total: results.length,
    passed,
    failed,
    skipped,
    results
  };
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testVerification };
