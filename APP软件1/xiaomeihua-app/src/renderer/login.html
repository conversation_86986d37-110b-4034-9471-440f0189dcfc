<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>小梅花AI智能客服 - 登录</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
    }
    
    html, body {
      height: 100%;
      width: 100%;
      overflow: hidden;
      background-color: transparent;
    }
    
    /* 主窗口容器 */
    .main-container {
      width: 420px;
      margin: 0 auto;
      border-radius: 20px;
      overflow: hidden;
    }
    
    /* 可拖动区域 */
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 25px;
      text-align: center;
      font-size: 24px;
      font-weight: bold;
      position: relative;
      -webkit-app-region: drag; /* 整个头部可拖动 */
      letter-spacing: 2px; /* 增加字间距 */
    }
    
    /* 窗口控制按钮区域 - macOS样式 */
    .window-controls {
      position: absolute;
      top: 15px;
      left: 15px;
      display: flex;
      z-index: 100;
      -webkit-app-region: no-drag; /* 按钮区域不可拖动 */
      padding: 5px;
      border-radius: 4px;
    }
    
    /* Windows窗口控制按钮 */
    .win-controls {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      height: 30px; /* 固定高度 */
      z-index: 100;
      -webkit-app-region: no-drag;
    }
    
    .win-control-btn {
      width: 46px;
      height: 30px; /* 固定高度 */
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: white;
      cursor: pointer;
      transition: background-color 0.2s, color 0.2s;
    }
    
    .win-control-btn:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
    
    #win-close-btn:hover {
      background-color: #e81123;
    }
    
    /* 当鼠标悬停在整个控制区域时，显示所有按钮的符号 */
    .window-controls:hover .close-button::before,
    .window-controls:hover .close-button::after,
    .window-controls:hover .minimize-button::before {
      background-color: rgba(0, 0, 0, 0.7);
    }
    
    .control-button {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 8px;
      color: rgba(0, 0, 0, 0);
      transition: all 0.2s ease;
      position: relative;
    }
    
    .close-button {
      background-color: #ff5f57;
    }
    
    .close-button:hover {
      background-color: #ff3b30;
    }
    
    .close-button::before,
    .close-button::after {
      content: '';
      position: absolute;
      width: 6px;
      height: 1px;
      background-color: rgba(0, 0, 0, 0);
      top: 50%;
      left: 50%;
      transition: background-color 0.2s ease;
    }
    
    .close-button::before {
      transform: translate(-50%, -50%) rotate(45deg);
    }
    
    .close-button::after {
      transform: translate(-50%, -50%) rotate(-45deg);
    }
    
    .minimize-button {
      background-color: #ffbd2e;
    }
    
    .minimize-button:hover {
      background-color: #ffb300;
    }
    
    .minimize-button::before {
      content: '';
      position: absolute;
      width: 6px;
      height: 1px;
      background-color: rgba(0, 0, 0, 0);
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      transition: background-color 0.2s ease;
    }
    
    .header-divider {
      height: 5px;
      background: linear-gradient(90deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
      background-size: 300% 300%;
      animation: gradientShift 3s ease infinite;
    }
    
    .body {
      padding: 30px;
      background: white;
    }
    
    .logo-container {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .logo {
      width: 100px;
      height: 100px;
      object-fit: cover;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #495057;
    }
    
    .form-input {
      width: 100%;
      padding: 15px;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    
    .form-input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    }

    /* 粘贴成功时的绿色边框动画 */
    .form-input.paste-success {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2) !important;
      transition: all 0.3s ease;
    }

    /* 输入框悬停效果 */
    .form-input:hover {
      border-color: #b8c6ea;
      transition: border-color 0.2s ease;
    }

    /* 占位符文本样式优化 */
    .form-input::placeholder {
      color: #9ca3af;
      font-style: italic;
    }
    
    .btn {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 7px 20px rgba(102, 126, 234, 0.4);
    }
    
    .btn:active {
      transform: translateY(1px);
    }
    
    .message {
      margin-top: 20px;
      padding: 12px;
      border-radius: 8px;
      text-align: center;
      font-weight: 600;
      display: none;
    }
    
    .success {
      background-color: rgba(40, 167, 69, 0.1);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.2);
    }
    
    .error {
      background-color: rgba(220, 53, 69, 0.1);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.2);
    }
    
    .loading {
      background-color: rgba(0, 123, 255, 0.1);
      color: #007bff;
      border: 1px solid rgba(0, 123, 255, 0.2);
    }
    
    .version {
      text-align: center;
      margin-top: 20px;
      font-size: 12px;
      color: #6c757d;
    }
    
    /* 加载动画 */
    .spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-right: 10px;
      vertical-align: middle;
    }
    
    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* 卡密到期提示样式 */
    .expiry-notice {
      margin-top: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      border-radius: 12px;
      color: white;
      text-align: center;
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
      animation: slideIn 0.3s ease-out;
    }

    .notice-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .notice-icon {
      font-size: 32px;
      margin-bottom: 5px;
    }

    .expiry-notice h3 {
      margin: 0;
      font-size: 18px;
      font-weight: bold;
    }

    .expiry-notice p {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
    }

    .notice-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }

    .btn-contact, .btn-close {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-contact {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .btn-contact:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .btn-close {
      background: rgba(0, 0, 0, 0.2);
      color: white;
    }

    .btn-close:hover {
      background: rgba(0, 0, 0, 0.3);
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* 协议弹窗样式已移除，现在使用独立窗口显示协议 */

    /* 协议内容样式已移除，现在使用独立窗口显示协议 */
  </style>
</head>
<body>
  <div class="main-container">
    <div class="header">
      <!-- macOS窗口控制按钮 -->
      <div class="window-controls">
        <div class="control-button close-button" id="close-button"></div>
        <div class="control-button minimize-button" id="minimize-button"></div>
      </div>
      <!-- Windows窗口控制按钮 -->
      <div class="win-controls">
        <div class="win-control-btn" id="win-minimize-btn">&#x2212;</div>
        <div class="win-control-btn" id="win-close-btn">&#x2715;</div>
      </div>
      小梅花AI智能客服
    </div>
    <div class="header-divider"></div>
    <div class="body">
      <div class="logo-container">
        <img src="../assets/logo.png" alt="小梅花AI智能客服" class="logo" id="logo">
      </div>
      
      <div class="form-group">
        <label for="license-key" class="form-label">请输入卡密</label>
        <input type="text" id="license-key" class="form-input" placeholder="请输入您的卡密" autocomplete="off" spellcheck="false">
      </div>
      
      <button id="verify-btn" class="btn">登录</button>
      
      <div id="message" class="message"></div>

      <!-- 卡密到期提示区域 -->
      <div id="expiry-notice" class="expiry-notice" style="display: none;">
        <div class="notice-content">
          <div class="notice-icon">⚠️</div>
          <h3>卡密到期提醒</h3>
          <p id="expiry-message">您的卡密已过期，请联系代理商续费</p>
          <div class="notice-actions">
            <button type="button" class="btn-contact" onclick="showContactInfo()">联系代理商</button>
            <button type="button" class="btn-close" onclick="hideExpiryNotice()">我知道了</button>
          </div>
        </div>
      </div>

      <div class="version">
        版本: v1.1.4 © 2025 小梅花AI科技
      </div>

      <!-- 添加用户协议复选框 -->
      <div style="margin-top: 15px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <input type="checkbox" id="agreement-checkbox" checked style="margin-right: 5px;">
          <label for="agreement-checkbox" style="font-size: 12px; color: #6c757d;">
            我已阅读并同意
          </label>
        </div>
        <div id="agreements-links-container" style="display: flex; flex-wrap: wrap; justify-content: center; gap: 8px; max-width: 400px;">
          <!-- 协议链接将动态加载到这里 -->
        </div>
      </div>
    </div>
  </div>
  
  <!-- 协议弹窗已移除，现在使用独立窗口显示协议 -->
  
  <!-- 引入版本更新器 -->
  <script src="version-updater.js"></script>

  <script>
    // DOM元素
    const licenseKeyInput = document.getElementById('license-key');
    const verifyBtn = document.getElementById('verify-btn');
    const messageDiv = document.getElementById('message');
    const logoImg = document.getElementById('logo');
    const closeBtn = document.getElementById('close-button');
    const minimizeBtn = document.getElementById('minimize-button');
    const winCloseBtn = document.getElementById('win-close-btn');
    const winMinimizeBtn = document.getElementById('win-minimize-btn');
    const winControls = document.querySelector('.win-controls');
    const macControls = document.querySelector('.window-controls');
    
    // 检测平台并设置适当的控制按钮可见性
    const platform = navigator.platform.toLowerCase();
    const isWindows = platform.includes('win');
    const isMac = platform.includes('mac');
    
    // 根据平台显示/隐藏相应的控制按钮
    if (isWindows) {
      if (macControls) macControls.style.display = 'none';
      if (winControls) winControls.style.display = 'flex';
    } else {
      if (winControls) winControls.style.display = 'none';
    }
    
    // macOS窗口控制按钮事件
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        window.xiaomeihuaAPI.closeWindow();
      });
    }
    
    if (minimizeBtn) {
      minimizeBtn.addEventListener('click', () => {
        window.xiaomeihuaAPI.minimizeWindow();
      });
    }
    
    // Windows窗口控制按钮事件
    if (winCloseBtn) {
      winCloseBtn.addEventListener('click', () => {
        window.xiaomeihuaAPI.closeWindow();
      });
    }
    
    if (winMinimizeBtn) {
      winMinimizeBtn.addEventListener('click', () => {
        window.xiaomeihuaAPI.minimizeWindow();
      });
    }


    
    // 设置Logo图片路径
    logoImg.onerror = function() {
      // 如果加载失败，尝试其他路径
      this.src = '../../build/icon.png';
    };
    
    // 显示消息
    function showMessage(message, type) {
      // 【优化】支持HTML内容，用于显示多行错误消息
      if (message.includes('<br>') || message.includes('<')) {
        messageDiv.innerHTML = message;
      } else {
        messageDiv.textContent = message;
      }
      messageDiv.className = `message ${type}`;
      messageDiv.style.display = 'block';
    }

    // 显示卡密到期提示
    function showExpiryNotice(message) {
      const expiryNotice = document.getElementById('expiry-notice');
      const expiryMessage = document.getElementById('expiry-message');

      if (expiryNotice && expiryMessage) {
        expiryMessage.textContent = message;
        expiryNotice.style.display = 'block';

        // 隐藏普通消息
        messageDiv.style.display = 'none';
      }
    }

    // 隐藏卡密到期提示
    function hideExpiryNotice() {
      const expiryNotice = document.getElementById('expiry-notice');
      if (expiryNotice) {
        expiryNotice.style.display = 'none';

        // 重新启用输入框
        licenseKeyInput.disabled = false;
        verifyBtn.disabled = false;
        verifyBtn.textContent = '登录';

        // 清空输入框
        licenseKeyInput.value = '';
        licenseKeyInput.focus();
      }
    }

    // 显示联系信息
    function showContactInfo() {
      showMessage('请联系您的代理商或客服人员进行卡密续费', 'info');

      // 可以在这里添加更多联系方式，比如打开联系页面等
      // window.xiaomeihuaAPI.openContactPage();
    }
    
    // 页面加载完成后，获取已保存的卡密并显示
    window.addEventListener('DOMContentLoaded', async () => {
      try {
        // 获取已保存的卡密
        const savedLicense = await window.xiaomeihuaAPI.getSavedLicense();
        if (savedLicense) {
          // 在输入框中显示已保存的卡密
          licenseKeyInput.value = savedLicense;
        }

        // 预加载协议标题，避免闪烁
        await preloadAgreementTitle();
      } catch (error) {
        console.error('获取已保存卡密失败:', error);
      }
    });
    
    // 验证卡密
    verifyBtn.addEventListener('click', () => {
      const licenseKey = licenseKeyInput.value.trim();
      
      if (!licenseKey) {
        showMessage('请输入卡密', 'error');
        return;
      }

      // 【优化】前端快速预检卡密格式
      if (licenseKey.length < 10) {
        showMessage('卡密格式不正确，长度过短', 'error');
        return;
      }

      if (!/^[A-Z0-9\-]+$/i.test(licenseKey)) {
        showMessage('卡密格式不正确，只能包含字母、数字和连字符', 'error');
        return;
      }
      
      // 检查是否勾选用户协议
      const agreementCheckbox = document.getElementById('agreement-checkbox');
      if (!agreementCheckbox.checked) {
        showMessage('请阅读并同意用户协议', 'error');
        return;
      }
      
      // 【优化】清除之前的错误状态
      const expiryNotice = document.querySelector('.expiry-notice');
      if (expiryNotice) {
        expiryNotice.remove();
      }

      // 显示加载中
      verifyBtn.disabled = true;
      verifyBtn.innerHTML = '<span class="spinner"></span>验证中...';
      showMessage('正在验证卡密，请稍候...', 'loading');

      // 【优化】添加验证开始时间，用于计算响应时间
      const startTime = Date.now();

      // 调用API验证卡密
      window.xiaomeihuaAPI.verifyLicense(licenseKey)
        .then(result => {
          // 计算响应时间
          const responseTime = Date.now() - startTime;
          console.log(`卡密验证响应时间: ${responseTime}ms`);
          if (result.success) {
            showMessage('验证成功，正在进入系统...', 'success');
          } else {
            // 【优化】增强错误消息显示，支持多行文本
            let errorMessage = result.message || '验证失败，请检查卡密是否正确';

            // 处理换行符，使错误消息更易读
            if (errorMessage.includes('\n')) {
              errorMessage = errorMessage.replace(/\n/g, '<br>');
            }

            // 【修复】卡密无效时显示底部弹窗提醒，但保持输入框和按钮可用
            if (result.force_exit || ['KEY_EXPIRED', 'KEY_DISABLED', 'KEY_NOT_FOUND'].includes(result.error_code)) {
              // 卡密过期或无效，显示特殊提示
              if (result.error_code === 'KEY_EXPIRED') {
                showExpiryNotice('卡密已过期，请联系代理商续费使用');
              } else if (result.error_code === 'KEY_DISABLED') {
                showExpiryNotice('卡密已被禁用，请联系代理商');
              } else if (result.error_code === 'KEY_NOT_FOUND') {
                showExpiryNotice('卡密不存在，请检查卡密是否正确');
              } else {
                showExpiryNotice(errorMessage);
              }

              // 【修复】保持输入框和按钮可用，允许用户重新输入卡密
              licenseKeyInput.disabled = false;
              verifyBtn.disabled = false;
              verifyBtn.textContent = '登录';
            } else {
              showMessage(errorMessage, 'error');
              verifyBtn.disabled = false;
              verifyBtn.textContent = '登录';
            }
          }
        })
        .catch(error => {
          console.error('验证卡密错误:', error);

          // 【优化】根据错误类型显示不同消息
          let errorMsg = '验证失败，请重试';
          if (error.message && error.message.includes('超时')) {
            errorMsg = '验证超时，请检查网络连接后重试';
          } else if (error.message && error.message.includes('网络')) {
            errorMsg = '网络连接失败，请检查网络设置';
          } else if (error.message) {
            errorMsg = error.message;
          }

          showMessage(errorMsg, 'error');
          verifyBtn.disabled = false;
          verifyBtn.textContent = '登录';
        });
    });
    
    // 监听键盘回车事件和空格限制 - 增强版本
    licenseKeyInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter') {
        verifyBtn.click();
        return;
      }

      // 静默禁止输入空格字符
      if (event.key === ' ' || event.key === 'Spacebar') {
        event.preventDefault();
        return false;
      }

      // 【强化】处理Ctrl+V粘贴快捷键
      if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'v') {
        console.log('🔥 检测到Ctrl+V快捷键，强制触发粘贴');

        // 不阻止默认行为，让paste事件正常触发
        // 但是添加一个小延迟来确保paste事件能被触发
        setTimeout(() => {
          // 如果paste事件没有被触发，手动处理
          if (!licenseKeyInput.dataset.pasteHandled) {
            console.log('🔄 paste事件可能未触发，手动处理粘贴');
            handleAsyncClipboardPaste();
          }
          // 清除标记
          delete licenseKeyInput.dataset.pasteHandled;
        }, 50);
      }

      // 处理Ctrl+A全选
      if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'a') {
        // 允许全选操作
        return;
      }
    });

    // 监听输入事件，静默过滤空格并清除错误状态
    licenseKeyInput.addEventListener('input', (event) => {
      const value = event.target.value;
      if (value.includes(' ')) {
        // 静默移除所有空格
        event.target.value = value.replace(/\s/g, '');
      }

      // 【新增】当用户修改卡密时，清除错误状态和底部弹窗
      const expiryNotice = document.querySelector('.expiry-notice');
      if (expiryNotice) {
        expiryNotice.remove();
      }

      // 清除错误消息
      const messageDiv = document.querySelector('.message');
      if (messageDiv && messageDiv.classList.contains('error')) {
        messageDiv.style.display = 'none';
      }

      // 确保按钮状态正常
      if (!verifyBtn.disabled || verifyBtn.textContent === '卡密无效') {
        verifyBtn.disabled = false;
        verifyBtn.textContent = '登录';
      }
    });

    // 【彻底修复】监听粘贴事件 - 确保快捷键粘贴正常工作
    licenseKeyInput.addEventListener('paste', (event) => {
      console.log('🎯 粘贴事件触发:', event);

      // 标记paste事件已被处理
      licenseKeyInput.dataset.pasteHandled = 'true';

      // 总是阻止默认行为，我们自己处理
      event.preventDefault();
      event.stopPropagation();

      try {
        let paste = '';

        // 方法1: 从事件中获取剪贴板数据
        if (event.clipboardData) {
          paste = event.clipboardData.getData('text/plain') ||
                  event.clipboardData.getData('text') ||
                  event.clipboardData.getData('Text');
        }

        // 方法2: IE兼容性
        if (!paste && window.clipboardData) {
          paste = window.clipboardData.getData('Text');
        }

        console.log('获取到粘贴内容:', paste ? `${paste.length}个字符` : '空');

        if (paste) {
          // 清理粘贴内容：移除所有空格和换行符
          const cleanPaste = paste.replace(/\s/g, '');

          // 设置清理后的内容
          event.target.value = cleanPaste;

          // 触发input事件
          const inputEvent = new Event('input', { bubbles: true });
          event.target.dispatchEvent(inputEvent);

          // 视觉反馈
          if (cleanPaste.length > 0) {
            event.target.classList.add('paste-success');
            setTimeout(() => {
              event.target.classList.remove('paste-success');
            }, 1500);
            console.log('✅ 粘贴成功:', cleanPaste.length, '个字符');
          }
        } else {
          console.warn('⚠️ 未能获取粘贴内容，尝试备用方案...');
          // 备用方案：使用异步剪贴板API
          handleAsyncClipboardPaste();
        }
      } catch (error) {
        console.error('❌ 粘贴处理失败:', error);
        // 备用方案
        handleAsyncClipboardPaste();
      }
    });
    
    // 【新增】异步剪贴板处理备用方案
    async function handleAsyncClipboardPaste() {
      try {
        let text = '';

        // 方法1: 现代浏览器Clipboard API
        if (navigator.clipboard && navigator.clipboard.readText) {
          try {
            text = await navigator.clipboard.readText();
            console.log('使用Clipboard API获取成功');
          } catch (clipError) {
            console.log('Clipboard API失败:', clipError.message);
          }
        }

        // 方法2: Electron IPC备用方案
        if (!text && window.xiaomeihuaAPI && window.xiaomeihuaAPI.getClipboardText) {
          try {
            text = await window.xiaomeihuaAPI.getClipboardText();
            console.log('使用Electron IPC获取成功');
          } catch (ipcError) {
            console.log('Electron IPC失败:', ipcError.message);
          }
        }

        if (text) {
          const cleanText = text.replace(/\s/g, '');
          licenseKeyInput.value = cleanText;

          // 触发input事件
          const inputEvent = new Event('input', { bubbles: true });
          licenseKeyInput.dispatchEvent(inputEvent);

          // 视觉反馈
          licenseKeyInput.classList.add('paste-success');
          setTimeout(() => {
            licenseKeyInput.classList.remove('paste-success');
          }, 1500);

          console.log('✅ 备用方案粘贴成功:', cleanText.length, '个字符');
        } else {
          console.error('❌ 所有粘贴方案都失败了');
        }
      } catch (error) {
        console.error('❌ 异步粘贴处理失败:', error);
      }
    }

    // 监听自动验证事件
    window.xiaomeihuaAPI.onAutoVerify((licenseKey) => {
      licenseKeyInput.value = licenseKey;
      verifyBtn.click();
    });

    // 【新增】监听自动填充卡密事件（仅填充，不自动验证）
    window.xiaomeihuaAPI.onAutoFillLicense((licenseKey) => {
      console.log('自动填充卡密:', licenseKey);
      licenseKeyInput.value = licenseKey;
      // 不自动点击验证按钮，让用户手动点击
    });
    
    // 用户协议功能（使用独立窗口）
    const showAgreement = document.getElementById('show-agreement');
    const agreementCheckbox = document.getElementById('agreement-checkbox');
    const agreementLinkTitle = document.getElementById('agreement-link-title');

    // 预加载协议标题的函数
    async function preloadAgreementTitle() {
      try {
        if (window.xiaomeihuaAPI && window.xiaomeihuaAPI.getPublishedAgreements) {
          const result = await window.xiaomeihuaAPI.getPublishedAgreements();
          if (result.success && result.agreements && result.agreements.length > 0) {
            // 显示所有协议链接
            const agreementsContainer = document.getElementById('agreements-links-container');
            if (agreementsContainer) {
              agreementsContainer.innerHTML = '';

              result.agreements.forEach((agreement, index) => {
                const agreementLink = document.createElement('span');
                agreementLink.style.cssText = `
                  display: inline-block;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;
                  padding: 6px 12px;
                  border-radius: 8px;
                  cursor: pointer;
                  text-decoration: none;
                  font-size: 12px;
                  font-weight: 600;
                  margin: 2px;
                  transition: all 0.3s ease;
                  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                  border: none;
                `;
                agreementLink.textContent = agreement.title;

                // 添加悬停效果
                agreementLink.addEventListener('mouseenter', () => {
                  agreementLink.style.transform = 'translateY(-1px)';
                  agreementLink.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
                });

                agreementLink.addEventListener('mouseleave', () => {
                  agreementLink.style.transform = 'translateY(0)';
                  agreementLink.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.3)';
                });

                // 添加点击事件
                agreementLink.addEventListener('click', async (e) => {
                  e.preventDefault();
                  try {
                    if (window.xiaomeihuaAPI.openAgreementWindow) {
                      await window.xiaomeihuaAPI.openAgreementWindow(agreement);
                    }
                  } catch (error) {
                    console.error('打开协议失败:', error);
                    alert('打开协议失败，请稍后重试');
                  }
                });

                agreementsContainer.appendChild(agreementLink);
              });
            }

            // 存储协议数据供后续使用
            window.cachedAgreements = result.agreements;
          }
        }
      } catch (error) {
        console.log('预加载协议标题失败:', error);
        // 保持隐藏状态
      }
    }

    // 协议链接点击事件已直接绑定到各个协议链接上，无需额外处理
    // 协议选择菜单已移除，现在直接显示所有协议链接

    // 页面加载完成后预加载协议标题
    setTimeout(() => {
      preloadAgreementTitle();
    }, 1000);

    // 增强输入框功能 - 确保粘贴功能完全可用
    function enhanceInputFeatures() {
      // 确保输入框可以接收焦点
      licenseKeyInput.setAttribute('tabindex', '0');

      // 添加右键菜单支持（contextmenu事件）
      licenseKeyInput.addEventListener('contextmenu', (event) => {
        // 允许右键菜单显示，用户可以通过右键菜单粘贴
        console.log('右键菜单已显示，用户可以选择粘贴');
      });

      // 添加focus和blur事件处理，提供视觉反馈
      licenseKeyInput.addEventListener('focus', () => {
        licenseKeyInput.style.borderColor = '#667eea';
        licenseKeyInput.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.2)';
      });

      licenseKeyInput.addEventListener('blur', () => {
        licenseKeyInput.style.borderColor = '#e1e5e9';
        licenseKeyInput.style.boxShadow = 'none';
      });

      // 添加双击全选功能
      licenseKeyInput.addEventListener('dblclick', () => {
        licenseKeyInput.select();
      });

      console.log('输入框增强功能已启用：支持粘贴、右键菜单、全选等功能');
    }





    // 页面加载完成后启用增强功能
    setTimeout(() => {
      enhanceInputFeatures();
    }, 100);
  </script>
</body>
</html> 